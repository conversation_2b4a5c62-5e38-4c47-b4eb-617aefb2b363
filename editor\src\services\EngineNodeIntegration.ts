/**
 * 引擎节点集成服务
 * 负责将新的节点集成到引擎的可视化脚本系统中
 */

import EngineService from './EngineService';

/**
 * 引擎节点集成类
 */
export class EngineNodeIntegration {
  private static instance: EngineNodeIntegration | null = null;
  private engineService: typeof EngineService | null = null;
  private isInitialized: boolean = false;

  /**
   * 获取单例实例
   */
  public static getInstance(): EngineNodeIntegration {
    if (!EngineNodeIntegration.instance) {
      EngineNodeIntegration.instance = new EngineNodeIntegration();
    }
    return EngineNodeIntegration.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 初始化集成服务
   * @param engineService 引擎服务实例
   */
  public initialize(engineService: typeof EngineService): void {
    this.engineService = engineService;
    this.registerBatch4Nodes();
    this.isInitialized = true;
    console.log('引擎节点集成服务已初始化');
  }

  /**
   * 注册第4批次节点到引擎
   */
  private registerBatch4Nodes(): void {
    if (!this.engineService) {
      console.error('引擎服务未初始化');
      return;
    }

    try {
      // 获取可视化脚本引擎
      const visualScriptEngine = this.engineService.getVisualScriptEngine();
      
      if (!visualScriptEngine) {
        console.error('可视化脚本引擎未初始化');
        return;
      }

      // 注册渲染相机节点的执行逻辑
      this.registerRenderingNodes(visualScriptEngine);

      console.log('第4批次节点已成功注册到引擎');
    } catch (error) {
      console.error('注册第4批次节点失败:', error);
    }
  }

  /**
   * 注册渲染节点的执行逻辑
   * @param visualScriptEngine 可视化脚本引擎
   */
  private registerRenderingNodes(visualScriptEngine: any): void {
    // 118. 创建透视相机节点
    this.registerNodeExecutor(visualScriptEngine, 'rendering/camera/createPerspectiveCamera', {
      execute: (inputs: any) => {
        try {
          const fov = inputs.fov || 75;
          const aspect = inputs.aspect || (window.innerWidth / window.innerHeight);
          const near = inputs.near || 0.1;
          const far = inputs.far || 1000;
          const entityName = inputs.entityName || '透视相机';

          // 创建相机实体
          const scene = this.engineService?.getActiveScene();
          if (!scene) {
            throw new Error('当前场景不存在');
          }

          const cameraEntity = scene.createEntity(entityName);
          
          // 添加相机组件
          const camera = cameraEntity.addComponent('Camera');
          camera.setType('perspective');
          camera.setFOV(fov);
          camera.setAspect(aspect);
          camera.setNear(near);
          camera.setFar(far);

          // 添加变换组件
          cameraEntity.addComponent('Transform');

          return {
            camera: camera,
            entity: cameraEntity
          };
        } catch (error) {
          console.error('创建透视相机失败:', error);
          throw error;
        }
      }
    });

    // 119. 创建正交相机节点
    this.registerNodeExecutor(visualScriptEngine, 'rendering/camera/createOrthographicCamera', {
      execute: (inputs: any) => {
        try {
          const left = inputs.left || -10;
          const right = inputs.right || 10;
          const top = inputs.top || 10;
          const bottom = inputs.bottom || -10;
          const near = inputs.near || 0.1;
          const far = inputs.far || 1000;
          const entityName = inputs.entityName || '正交相机';

          // 创建相机实体
          const scene = this.engineService?.getActiveScene();
          if (!scene) {
            throw new Error('当前场景不存在');
          }

          const cameraEntity = scene.createEntity(entityName);
          
          // 添加相机组件
          const camera = cameraEntity.addComponent('Camera');
          camera.setType('orthographic');
          camera.setLeft(left);
          camera.setRight(right);
          camera.setTop(top);
          camera.setBottom(bottom);
          camera.setNear(near);
          camera.setFar(far);

          // 添加变换组件
          cameraEntity.addComponent('Transform');

          return {
            camera: camera,
            entity: cameraEntity
          };
        } catch (error) {
          console.error('创建正交相机失败:', error);
          throw error;
        }
      }
    });

    // 120. 设置相机位置节点
    this.registerNodeExecutor(visualScriptEngine, 'rendering/camera/setCameraPosition', {
      execute: (inputs: any) => {
        try {
          const entity = inputs.entity;
          const position = inputs.position || { x: 0, y: 0, z: 5 };

          if (!entity) {
            throw new Error('相机实体不能为空');
          }

          // 获取变换组件
          const transform = entity.getComponent('Transform');
          if (!transform) {
            throw new Error('相机实体缺少变换组件');
          }

          // 设置位置
          transform.setPosition(position.x, position.y, position.z);

          return { entity: entity };
        } catch (error) {
          console.error('设置相机位置失败:', error);
          throw error;
        }
      }
    });
  }

  /**
   * 注册节点执行器
   * @param visualScriptEngine 可视化脚本引擎
   * @param nodeType 节点类型
   * @param executor 执行器
   */
  private registerNodeExecutor(visualScriptEngine: any, nodeType: string, executor: any): void {
    if (visualScriptEngine && typeof visualScriptEngine.registerNodeExecutor === 'function') {
      visualScriptEngine.registerNodeExecutor(nodeType, executor);
      console.log(`节点执行器已注册: ${nodeType}`);
    } else {
      console.warn(`无法注册节点执行器: ${nodeType} - 可视化脚本引擎不支持registerNodeExecutor方法`);
    }
  }

  /**
   * 创建测试脚本
   * 用于测试新注册的渲染节点
   */
  public createTestScript(): any {
    if (!this.engineService) {
      console.error('引擎服务未初始化');
      return null;
    }

    try {
      const visualScriptEngine = this.engineService.getVisualScriptEngine();
      if (!visualScriptEngine) {
        console.error('可视化脚本引擎未初始化');
        return null;
      }

      // 创建测试脚本
      const testScript = visualScriptEngine.createScript('渲染节点测试脚本');
      
      // 添加测试节点
      const startNode = testScript.addNode('core/events/onStart');
      const createCameraNode = testScript.addNode('rendering/camera/createPerspectiveCamera');
      const setCameraPositionNode = testScript.addNode('rendering/camera/setCameraPosition');

      // 连接节点
      testScript.connectNodes(startNode, 'onComplete', createCameraNode, 'execute');
      testScript.connectNodes(createCameraNode, 'onComplete', setCameraPositionNode, 'execute');
      testScript.connectNodes(createCameraNode, 'entity', setCameraPositionNode, 'entity');

      // 设置节点参数
      createCameraNode.setInputValue('fov', 60);
      createCameraNode.setInputValue('entityName', '测试透视相机');
      setCameraPositionNode.setInputValue('position', { x: 0, y: 5, z: 10 });

      console.log('测试脚本创建成功');
      return testScript;
    } catch (error) {
      console.error('创建测试脚本失败:', error);
      return null;
    }
  }

  /**
   * 执行测试脚本
   */
  public async executeTestScript(): Promise<boolean> {
    try {
      const testScript = this.createTestScript();
      if (!testScript) {
        return false;
      }

      const result = await this.engineService?.executeVisualScript(testScript);
      console.log('测试脚本执行结果:', result);
      return true;
    } catch (error) {
      console.error('执行测试脚本失败:', error);
      return false;
    }
  }

  /**
   * 获取集成状态
   */
  public getStatus(): {
    isInitialized: boolean;
    engineConnected: boolean;
    registeredNodes: string[];
  } {
    const registeredNodes = [
      'rendering/camera/createPerspectiveCamera',
      'rendering/camera/createOrthographicCamera',
      'rendering/camera/setCameraPosition'
    ];

    return {
      isInitialized: this.isInitialized,
      engineConnected: this.engineService !== null,
      registeredNodes: this.isInitialized ? registeredNodes : []
    };
  }

  /**
   * 销毁集成服务
   */
  public dispose(): void {
    this.engineService = null;
    this.isInitialized = false;
    console.log('引擎节点集成服务已销毁');
  }
}

// 导出单例实例
export const engineNodeIntegration = EngineNodeIntegration.getInstance();
